// import { useState } from "react";
// // import { Modal, Button, Form } from "react-bootstrap";
// import axios from "axios";

// export function BabyModal({ isOpen, onClose, onSave }) {
//   const [name, setName] = useState("");
//   const [birthdate, setBirthdate] = useState("");

//   const handleSubmit = (e) => {
//     e.preventDefault();

//     axios.post("/babies", {
//       name: name,
//       birthdate: birthdate
//     })
//     .then(response => {
//       onSave(response.data); // Send the saved baby data back to parent
//       setName("");
//       setBirthdate("");
//     })
//     .catch(error => {
//       console.error("Error saving baby info:", error);
//     });
//   };

//   return (
//     <Modal show={isOpen} onHide={onClose}>
//       <Modal.Header closeButton>
//         <Modal.Title>Enter Your Baby's Information</Modal.Title>
//       </Modal.Header>
//       <Modal.Body>
//         <Form onSubmit={handleSubmit}>
//           <Form.Group className="mb-3">
//             <Form.Label>Baby's Name</Form.Label>
//             <Form.Control
//               type="text"
//               placeholder="Enter baby's name"
//               value={name}
//               onChange={(e) => setName(e.target.value)}
//               required
//             />
//           </Form.Group>

//           <Form.Group className="mb-3">
//             <Form.Label>Birthdate</Form.Label>
//             <Form.Control
//               type="date"
//               value={birthdate}
//               onChange={(e) => setBirthdate(e.target.value)}
//               required
//             />
//           </Form.Group>

//           <Button variant="primary" type="submit">
//             Save Baby Info
//           </Button>
//         </Form>
//       </Modal.Body>
//     </Modal>
//   );
// }
