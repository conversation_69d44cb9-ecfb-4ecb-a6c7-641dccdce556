export function FoodLogNew({onCreate}) {

  const handleSubmit = (event) => {
  event.preventDefault();
  const form = event.target;
  const params = new FormData(form);
  const successCallback = () => form.reset();
  onCreate(params, successCallback);  
  };

  return (
    <div>
      <h1>Add new food log:</h1>
      <form onSubmit={handleSubmit}>
        <div>
          Food: <input name="food_log.food" type="text"/>
        </div>
        <div>
          Reaction: <input name="food_log.reaction" type="text"/>
        </div>
        <div>
          Notes: <input name="food_log.notes" type="text"/>
        </div>
        <button type="submit">Create</button>
      </form>
    </div>

  )
}