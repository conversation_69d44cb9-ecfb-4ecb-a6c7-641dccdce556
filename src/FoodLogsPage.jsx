import axios from "axios";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { FoodLogIndex } from "./FoodLogIndex";
import { FoodLogNew } from "./FoodLogNew";

export function FoodLogsPage() {
  const [foodLogs, setFoodLogs] = useState([]);

  const handleIndex = () => {
    console.log("handleIndex");
    axios.get("/food_logs.json").then((response) => {
      console.log(response.data);
      setFoodLogs(response.data);
    });
  }

  const handleCreate = (params, successCallback) => {
    console.log("handleCreate");
    axios.post("/food_logs.json", params).then((response) => {
      setFoodLogs([...foodLogs, response.data]);
      successCallback();
    });
  }

  useEffect(handleIndex, []);

  return (
    <main>
      <FoodLogIndex food_logs={foodLogs} />
      <FoodLogNew onCreate={handleCreate} />
    </main>
  )
}