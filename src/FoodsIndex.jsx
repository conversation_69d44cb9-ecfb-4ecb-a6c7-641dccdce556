export function FoodsIndex({ foods, onShow }) {
  return (
    <div className="container mt-4">
      <h1 className="mb-4">All Foods:</h1>
      <div className="row g-3">
        {foods.map((food) => (
          <div className="col-sm-6 col-md-4 col-lg-3" key={food.id}>
            <div className="card shadow-sm">
              {food.image_url && (
                <img
                  src={food.image_url}
                  className="card-img-top"
                  alt={food.name}
                  style={{
                    height: "140px",
                    objectFit: "cover"
                  }}
                />
              )}
              <div className="card-body py-2">
                <h6 className="card-title mb-1">{food.name}</h6>
                <p
                  className="card-text text-muted mb-2"
                  style={{ fontSize: "0.85rem" }}
                >
                  {food.category}
                </p>
              </div>
              <div className="card-footer bg-transparent border-0 py-2 d-flex justify-content-end">
                <button
                  className="btn btn-sm btn-primary"
                  onClick={() => onShow(food)}
                  style={{ padding: "0.25rem 0.5rem", fontSize: "0.85rem" }}
                >
                  +
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
